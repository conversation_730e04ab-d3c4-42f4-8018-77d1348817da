# 固定距离LOD使用指南

## 快速开始

现在您可以在 `update_tileset_lod_visibility()` 方法中使用固定距离LOD选择了！

### 基本用法

```python
from lod_scheduler import LODScheduler, TilesetLODManager

# 创建调度器和管理器
scheduler = LODScheduler(stage, camera_path="/World/Camera")
tileset_manager = TilesetLODManager(stage, lod_scheduler=scheduler)

# 使用固定距离方法（默认距离范围）
result = tileset_manager.update_tileset_lod_visibility(
    use_fixed_distance=True,  # 启用固定距离方法
    verbose=True
)
```

### 自定义距离范围

```python
# 定义自定义距离范围
custom_ranges = {
    "High": (0, 30),        # 0-30米：高质量
    "Medium": (30, 100),    # 30-100米：中质量
    "Low": (100, 200),      # 100-200米：低质量
    "VeryLow": (200, float('inf'))  # 200米以上：极低质量
}

# 使用自定义距离范围
result = tileset_manager.update_tileset_lod_visibility(
    use_fixed_distance=True,
    distance_ranges=custom_ranges,
    verbose=True
)
```

### 对比SSE方法

```python
# 使用固定距离方法
result_distance = tileset_manager.update_tileset_lod_visibility(
    use_fixed_distance=True, verbose=True
)

# 使用SSE方法
result_sse = tileset_manager.update_tileset_lod_visibility(
    use_fixed_distance=False, verbose=True
)

print(f"固定距离方法选择: {result_distance[0]}")
print(f"SSE方法选择: {result_sse[0]}")
```

## 方法参数说明

### `update_tileset_lod_visibility()` 新参数

- **`use_fixed_distance`** (bool, 默认=False): 
  - `True`: 使用固定距离方法
  - `False`: 使用SSE方法（原有行为）

- **`distance_ranges`** (Dict, 可选): 自定义距离范围配置
  - 仅在 `use_fixed_distance=True` 时生效
  - 格式: `{"High": (min_dist, max_dist), ...}`

- **`verbose`** (bool, 默认=True): 是否输出详细信息

## 默认距离配置

```python
default_ranges = {
    "High": (0, 50),        # 0-50米：高质量LOD
    "Medium": (50, 150),    # 50-150米：中质量LOD
    "Low": (150, 300),      # 150-300米：低质量LOD
    "VeryLow": (300, float('inf'))  # 300米以上：极低质量LOD
}
```

## 不同场景的推荐配置

### 1. 近距离场景（室内、小物体）
```python
indoor_ranges = {
    "High": (0, 25),
    "Medium": (25, 80),
    "Low": (80, 180),
    "VeryLow": (180, float('inf'))
}
```

### 2. 远距离场景（地形、大型建筑）
```python
outdoor_ranges = {
    "High": (0, 80),
    "Medium": (80, 200),
    "Low": (200, 500),
    "VeryLow": (500, float('inf'))
}
```

### 3. 性能优先（移动设备）
```python
performance_ranges = {
    "High": (0, 20),
    "Medium": (20, 50),
    "Low": (50, 100),
    "VeryLow": (100, float('inf'))
}
```

### 4. 质量优先（高端设备）
```python
quality_ranges = {
    "High": (0, 100),
    "Medium": (100, 250),
    "Low": (250, 400),
    "VeryLow": (400, float('inf'))
}
```

## 输出示例

### 固定距离方法输出
```
=== Updating Hierarchical Tileset LOD Visibility with Per-Tile LOD Selection (Fixed Distance) ===

📊 LOD Update Summary (Smart Consistency Strategy - Fixed Distance):
  Strategy: Per-Tile Selection + Smart Consistency Rules
  LOD Selection Method: Fixed Distance
  Mixed LOD Detected: No
  Final LOD Distribution: {'High': 0, 'Medium': 15, 'Low': 0, 'VeryLow': 0}
  Most Common LOD: Medium
  Camera to region center distance: 75.3m
  Average tile distance: 82.1m
  Visible content nodes: 15
  Hidden content nodes: 45
  Frustum culled nodes: 0
  Frustum culling: Enabled
```

### SSE方法输出
```
=== Updating Hierarchical Tileset LOD Visibility with Per-Tile LOD Selection (SSE) ===

📊 LOD Update Summary (Smart Consistency Strategy - SSE):
  Strategy: Per-Tile Selection + Smart Consistency Rules
  LOD Selection Method: SSE
  Mixed LOD Detected: Yes
  Primary LOD: Medium
  Active LODs: ['High', 'Medium']
  Final LOD Distribution: {'High': 3, 'Medium': 12, 'Low': 0, 'VeryLow': 0}
  Most Common LOD: Medium
  Camera to region center distance: 75.3m
  Average SSE: 8.45px
  Visible content nodes: 15
  Hidden content nodes: 45
  Frustum culled nodes: 0
  Frustum culling: Enabled
```

## 优势对比

| 特性 | 固定距离方法 | SSE方法 |
|------|-------------|---------|
| **计算复杂度** | 极低 | 中等 |
| **配置难度** | 简单直观 | 需要理解SSE概念 |
| **性能开销** | 最小 | 中等 |
| **稳定性** | 高（基于距离） | 中等（受视角影响） |
| **适用场景** | 简单场景、性能优先 | 复杂场景、质量优先 |
| **可预测性** | 高 | 中等 |

## 何时使用固定距离方法

✅ **推荐使用的情况:**
- 性能要求高的应用
- 移动设备或低端硬件
- 简单的LOD需求
- 需要可预测的LOD行为
- 快速原型开发

❌ **不推荐使用的情况:**
- 需要精确的视觉质量控制
- 复杂的视角变化场景
- 对视觉质量要求极高的应用

## 运行示例

查看完整示例：
```bash
python lod/example_fixed_distance_lod.py
```

这个示例包含：
1. 基本LOD选择演示
2. 场景LOD更新演示  
3. 分层Tileset LOD演示
4. 方法对比演示

## 注意事项

1. **距离计算**: 基于包围盒中心到相机的欧几里得距离
2. **范围设置**: 确保距离范围连续且不重叠
3. **单位**: 距离单位为米（世界坐标单位）
4. **兼容性**: 与现有SSE方法完全兼容，可以随时切换
5. **稳定性**: 内置滞后机制，减少边界处的LOD抖动
