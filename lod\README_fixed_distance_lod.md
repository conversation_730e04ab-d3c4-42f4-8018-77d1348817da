# 固定距离LOD选择功能

## 概述

新增了基于固定距离区间的LOD（Level of Detail）选择功能，与屏幕误差（SSE）和几何误差无关，纯粹基于相机到对象的距离进行LOD级别选择。

## 主要特性

- **简单直观**: 基于距离区间，易于理解和配置
- **独立于SSE**: 不依赖屏幕误差计算，适合不需要复杂视觉质量计算的场景
- **可自定义**: 支持自定义距离范围配置
- **稳定性控制**: 内置滞后机制，减少LOD级别在边界处的抖动
- **兼容现有系统**: 与现有的SSE方法并存，不影响原有功能

## 新增方法

### 1. `select_lod_by_fixed_distance()`

基于固定距离区间选择LOD级别的核心方法。

```python
def select_lod_by_fixed_distance(self, bounding_box: BoundingBox, camera_position: Gf.Vec3f,
                               distance_ranges: Dict[str, Tuple[float, float]] = None,
                               verbose: bool = True, previous_lod: str = None) -> Tuple[str, Dict[str, any]]:
```

**参数说明:**
- `bounding_box`: 对象的包围盒
- `camera_position`: 相机位置
- `distance_ranges`: 自定义距离范围字典（可选）
- `verbose`: 是否输出详细信息
- `previous_lod`: 之前的LOD级别，用于稳定性检查

**返回值:**
- `tuple`: (选择的LOD级别, 距离信息字典)

### 2. `update_lod_visibility_by_fixed_distance()`

使用固定距离策略更新整个场景的LOD可见性。

```python
def update_lod_visibility_by_fixed_distance(self, region_bounds: BoundingBox = None, 
                                          distance_ranges: Dict[str, Tuple[float, float]] = None,
                                          verbose: bool = True):
```

## 默认距离配置

```python
distance_ranges = {
    "High": (0, 50),        # 0-50米：高质量LOD
    "Medium": (50, 150),    # 50-150米：中质量LOD
    "Low": (150, 300),      # 150-300米：低质量LOD
    "VeryLow": (300, float('inf'))  # 300米以上：极低质量LOD
}
```

## 使用示例

### 基本使用

```python
from lod_scheduler import LODScheduler, BoundingBox
from pxr import Gf

# 创建调度器
scheduler = LODScheduler(stage, camera_path="/World/Camera")

# 定义对象边界
bounds = BoundingBox(
    min_point=Gf.Vec3f(-50, -50, -50),
    max_point=Gf.Vec3f(50, 50, 50)
)

# 获取相机位置
camera_pos = scheduler.get_camera_position()

# 选择LOD（使用默认距离范围）
selected_lod, distance_info = scheduler.select_lod_by_fixed_distance(
    bounds, camera_pos, verbose=True
)

print(f"选择的LOD级别: {selected_lod}")
```

### 自定义距离范围

```python
# 定义自定义距离范围
custom_ranges = {
    "High": (0, 30),        # 更严格的高质量范围
    "Medium": (30, 100),    # 中等范围
    "Low": (100, 200),      # 低质量范围
    "VeryLow": (200, float('inf'))  # 极低质量范围
}

# 使用自定义范围选择LOD
selected_lod, distance_info = scheduler.select_lod_by_fixed_distance(
    bounds, camera_pos, distance_ranges=custom_ranges, verbose=True
)
```

### 更新场景LOD可见性

```python
# 使用固定距离方法更新整个场景的LOD可见性
result = scheduler.update_lod_visibility_by_fixed_distance(verbose=True)

if result[0]:  # 检查是否成功
    selected_lod, center_distance, _ = result
    print(f"LOD更新成功: {selected_lod}, 距离: {center_distance:.1f}m")
```

## 配置建议

### 不同场景的推荐配置

1. **近距离场景**（室内、小物体）:
   ```python
   {
       "High": (0, 25),
       "Medium": (25, 80),
       "Low": (80, 180),
       "VeryLow": (180, float('inf'))
   }
   ```

2. **远距离场景**（地形、大型建筑）:
   ```python
   {
       "High": (0, 80),
       "Medium": (80, 200),
       "Low": (200, 500),
       "VeryLow": (500, float('inf'))
   }
   ```

3. **性能优先**（移动设备、低端硬件）:
   ```python
   {
       "High": (0, 20),
       "Medium": (20, 50),
       "Low": (50, 100),
       "VeryLow": (100, float('inf'))
   }
   ```

## 稳定性特性

- **滞后机制**: 当LOD级别在边界附近时，使用10%的滞后因子防止频繁切换
- **边界扩展**: 对当前LOD级别扩展边界范围，增加稳定性
- **平滑过渡**: 减少LOD切换时的视觉跳跃

## 与SSE方法的对比

| 特性 | 固定距离方法 | SSE方法 |
|------|-------------|---------|
| 计算复杂度 | 低 | 中等 |
| 配置难度 | 简单 | 复杂 |
| 视觉质量控制 | 基础 | 精确 |
| 性能开销 | 最小 | 中等 |
| 适用场景 | 简单场景、性能优先 | 复杂场景、质量优先 |

## 运行示例

查看 `example_fixed_distance_lod.py` 文件获取完整的使用示例。

```bash
# 在Isaac Sim中运行示例
python lod/example_fixed_distance_lod.py
```

## 注意事项

1. 距离计算基于包围盒中心点到相机的欧几里得距离
2. 距离范围应该连续且不重叠
3. 建议根据具体场景调整距离阈值
4. 可以与现有的SSE方法并存使用，根据需要选择合适的方法
