#!/usr/bin/env python3
"""
固定距离LOD选择示例

这个脚本演示如何使用新添加的基于固定距离区间的LOD选择方法。
与屏幕误差(SSE)和几何误差无关，纯粹基于相机到对象的距离。
"""

import omni.usd
from pxr import Usd, UsdGeom, Gf
from lod_scheduler import LODScheduler, BoundingBox
from lod_config import get_default_lod_config

def example_fixed_distance_lod():
    """演示固定距离LOD选择的使用方法"""
    
    # 获取当前stage
    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("ERROR: No USD stage found")
        return
    
    # 创建LOD调度器
    config = get_default_lod_config()
    scheduler = LODScheduler(stage, camera_path="/World/Camera", centralized_config=config)
    
    # 定义一个示例包围盒（可以替换为实际的场景对象）
    example_bounds = BoundingBox(
        min_point=Gf.Vec3f(-100, -100, -100),
        max_point=Gf.Vec3f(100, 100, 100)
    )
    
    # 获取相机位置
    camera_position = scheduler.get_camera_position()
    if not camera_position:
        print("ERROR: Failed to get camera position")
        return
    
    print("=== 固定距离LOD选择示例 ===")
    print(f"相机位置: {camera_position}")
    print(f"对象边界: {example_bounds.min_point} 到 {example_bounds.max_point}")
    
    # 1. 使用默认距离范围
    print("\n1. 使用默认距离范围:")
    selected_lod, distance_info = scheduler.select_lod_by_fixed_distance(
        example_bounds, camera_position, verbose=True
    )
    
    # 2. 使用自定义距离范围
    print("\n2. 使用自定义距离范围:")
    custom_ranges = {
        "High": (0, 30),        # 0-30米：高质量
        "Medium": (30, 100),    # 30-100米：中质量  
        "Low": (100, 200),      # 100-200米：低质量
        "VeryLow": (200, float('inf'))  # 200米以上：极低质量
    }
    
    selected_lod_custom, distance_info_custom = scheduler.select_lod_by_fixed_distance(
        example_bounds, camera_position, distance_ranges=custom_ranges, verbose=True
    )
    
    # 3. 对比SSE方法的结果
    print("\n3. 对比SSE方法的结果:")
    selected_lod_sse, lod_sse_info = scheduler.select_lod_by_sse_and_distance(
        example_bounds, camera_position, verbose=True
    )
    
    # 4. 总结对比
    print("\n=== 方法对比总结 ===")
    print(f"固定距离方法（默认范围）: {selected_lod}")
    print(f"固定距离方法（自定义范围）: {selected_lod_custom}")
    print(f"SSE方法: {selected_lod_sse}")
    
    return selected_lod, selected_lod_custom, selected_lod_sse

def example_update_scene_lod_by_distance():
    """演示如何更新整个场景的LOD可见性（基于固定距离）"""

    # 获取当前stage
    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("ERROR: No USD stage found")
        return

    # 创建LOD调度器
    config = get_default_lod_config()
    scheduler = LODScheduler(stage, camera_path="/World/Camera", centralized_config=config)

    print("=== 更新场景LOD可见性（固定距离方法）===")

    # 使用默认距离范围更新LOD可见性
    print("\n使用默认距离范围:")
    result = scheduler.update_lod_visibility_by_fixed_distance(verbose=True)

    if result[0]:  # 如果成功
        selected_lod, center_distance, _ = result
        print(f"\n✅ LOD更新成功!")
        print(f"   选择的LOD级别: {selected_lod}")
        print(f"   到中心的距离: {center_distance:.1f}m")
    else:
        print("❌ LOD更新失败")

    # 使用自定义距离范围
    print("\n" + "="*50)
    print("使用自定义距离范围:")
    custom_ranges = {
        "High": (0, 25),        # 更严格的高质量范围
        "Medium": (25, 80),     # 中等范围
        "Low": (80, 180),       # 低质量范围
        "VeryLow": (180, float('inf'))  # 极低质量范围
    }

    result_custom = scheduler.update_lod_visibility_by_fixed_distance(
        distance_ranges=custom_ranges, verbose=True
    )

    if result_custom[0]:  # 如果成功
        selected_lod_custom, center_distance_custom, _ = result_custom
        print(f"\n✅ 自定义LOD更新成功!")
        print(f"   选择的LOD级别: {selected_lod_custom}")
        print(f"   到中心的距离: {center_distance_custom:.1f}m")
    else:
        print("❌ 自定义LOD更新失败")

def example_tileset_lod_with_fixed_distance():
    """演示如何在分层tileset中使用固定距离方法"""

    # 获取当前stage
    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("ERROR: No USD stage found")
        return

    # 创建LOD调度器和Tileset管理器
    config = get_default_lod_config()
    scheduler = LODScheduler(stage, camera_path="/World/Camera", centralized_config=config)

    # 导入TilesetLODManager
    from lod_scheduler import TilesetLODManager
    tileset_manager = TilesetLODManager(stage, camera_path="/World/Camera",
                                       lod_scheduler=scheduler, config=config)

    print("=== 分层Tileset LOD更新（固定距离方法）===")

    # 使用固定距离方法更新tileset LOD
    print("\n1. 使用默认固定距离范围:")
    result = tileset_manager.update_tileset_lod_visibility(
        use_fixed_distance=True, verbose=True
    )

    if result[0]:
        selected_lod, center_distance, avg_distance = result
        print(f"\n✅ Tileset LOD更新成功!")
        print(f"   最常见LOD级别: {selected_lod}")
        print(f"   到区域中心距离: {center_distance:.1f}m")
        print(f"   平均tile距离: {avg_distance:.1f}m")
    else:
        print("❌ Tileset LOD更新失败")

    # 使用自定义距离范围
    print("\n" + "="*60)
    print("2. 使用自定义固定距离范围:")
    custom_ranges = {
        "High": (0, 40),        # 0-40米：高质量
        "Medium": (40, 120),    # 40-120米：中质量
        "Low": (120, 250),      # 120-250米：低质量
        "VeryLow": (250, float('inf'))  # 250米以上：极低质量
    }

    result_custom = tileset_manager.update_tileset_lod_visibility(
        use_fixed_distance=True, distance_ranges=custom_ranges, verbose=True
    )

    if result_custom[0]:
        selected_lod_custom, center_distance_custom, avg_distance_custom = result_custom
        print(f"\n✅ 自定义Tileset LOD更新成功!")
        print(f"   最常见LOD级别: {selected_lod_custom}")
        print(f"   到区域中心距离: {center_distance_custom:.1f}m")
        print(f"   平均tile距离: {avg_distance_custom:.1f}m")
    else:
        print("❌ 自定义Tileset LOD更新失败")

    # 对比SSE方法
    print("\n" + "="*60)
    print("3. 对比SSE方法:")
    result_sse = tileset_manager.update_tileset_lod_visibility(
        use_fixed_distance=False, verbose=True
    )

    if result_sse[0]:
        selected_lod_sse, center_distance_sse, avg_sse = result_sse
        print(f"\n✅ SSE方法更新成功!")
        print(f"   最常见LOD级别: {selected_lod_sse}")
        print(f"   到区域中心距离: {center_distance_sse:.1f}m")
        print(f"   平均SSE: {avg_sse:.2f}px")
    else:
        print("❌ SSE方法更新失败")

    # 总结对比
    print("\n" + "="*60)
    print("📊 方法对比总结:")
    if result[0] and result_custom[0] and result_sse[0]:
        print(f"  固定距离（默认）: {result[0]}")
        print(f"  固定距离（自定义）: {result_custom[0]}")
        print(f"  SSE方法: {result_sse[0]}")

    return result, result_custom, result_sse

def print_distance_ranges_comparison():
    """打印不同距离范围配置的对比"""
    
    print("=== 距离范围配置对比 ===")
    
    configs = {
        "默认配置": {
            "High": (0, 50),
            "Medium": (50, 150), 
            "Low": (150, 300),
            "VeryLow": (300, float('inf'))
        },
        "近距离优化": {
            "High": (0, 25),
            "Medium": (25, 80),
            "Low": (80, 180), 
            "VeryLow": (180, float('inf'))
        },
        "远距离优化": {
            "High": (0, 80),
            "Medium": (80, 200),
            "Low": (200, 500),
            "VeryLow": (500, float('inf'))
        },
        "性能优先": {
            "High": (0, 20),
            "Medium": (20, 50),
            "Low": (50, 100),
            "VeryLow": (100, float('inf'))
        }
    }
    
    for config_name, ranges in configs.items():
        print(f"\n{config_name}:")
        for lod_name in ["High", "Medium", "Low", "VeryLow"]:
            min_dist, max_dist = ranges[lod_name]
            max_dist_str = f"{max_dist:.0f}m" if max_dist != float('inf') else "∞"
            print(f"  {lod_name:8}: {min_dist:3.0f}m - {max_dist_str}")

if __name__ == "__main__":
    print("固定距离LOD选择示例脚本")
    print("请在Isaac Sim中运行此脚本")

    # 打印配置对比
    print_distance_ranges_comparison()

    # 运行示例（需要在Isaac Sim环境中）
    try:
        # 基本LOD选择示例
        print("\n" + "="*80)
        example_fixed_distance_lod()

        print("\n" + "="*80)

        # 场景LOD更新示例
        example_update_scene_lod_by_distance()

        print("\n" + "="*80)

        # 分层Tileset LOD示例
        example_tileset_lod_with_fixed_distance()

    except Exception as e:
        print(f"示例运行出错（可能需要在Isaac Sim环境中运行）: {e}")
        import traceback
        traceback.print_exc()
