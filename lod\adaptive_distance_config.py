"""
自适应距离计算配置
允许用户调整自适应距离计算的各种参数
"""

from dataclasses import dataclass
from typing import Dict, Tuple


@dataclass
class AdaptiveDistanceConfig:
    """自适应距离计算配置类"""
    
    # 边界检测参数
    boundary_threshold_factor: float = 0.3  # 边界阈值因子（相对于最大尺寸）
    
    # 形状分析参数
    elongated_aspect_ratio: float = 2.0  # 细长形状的长宽比阈值
    
    # 距离混合权重 - 策略A: 接近边界且形状细长
    strategy_a_surface_weight: float = 0.8
    strategy_a_center_weight: float = 0.2
    
    # 距离混合权重 - 策略B: 接近边界但形状规整
    strategy_b_surface_weight: float = 0.6
    strategy_b_center_weight: float = 0.4
    
    # 距离混合权重 - 策略C: 远离边界但形状细长
    strategy_c_surface_weight: float = 0.4
    strategy_c_center_weight: float = 0.6
    
    # 距离混合权重 - 策略D: 远离边界且形状规整
    strategy_d_surface_weight: float = 0.2
    strategy_d_center_weight: float = 0.8
    
    # 最小距离约束
    min_distance_factor: float = 0.1  # 相对于平均尺寸的最小距离因子
    
    # 调试选项
    enable_debug_output: bool = False
    log_strategy_selection: bool = False


def get_default_adaptive_config() -> AdaptiveDistanceConfig:
    """获取默认的自适应距离配置"""
    return AdaptiveDistanceConfig()


def get_aggressive_adaptive_config() -> AdaptiveDistanceConfig:
    """获取激进的自适应配置（更强调表面距离）"""
    config = AdaptiveDistanceConfig()
    
    # 更大的边界阈值，更早触发自适应
    config.boundary_threshold_factor = 0.5
    
    # 更低的细长形状阈值
    config.elongated_aspect_ratio = 1.5
    
    # 更强调表面距离的权重
    config.strategy_a_surface_weight = 0.9
    config.strategy_a_center_weight = 0.1
    
    config.strategy_b_surface_weight = 0.8
    config.strategy_b_center_weight = 0.2
    
    config.strategy_c_surface_weight = 0.6
    config.strategy_c_center_weight = 0.4
    
    return config


def get_conservative_adaptive_config() -> AdaptiveDistanceConfig:
    """获取保守的自适应配置（更接近传统方法）"""
    config = AdaptiveDistanceConfig()
    
    # 更小的边界阈值，较少触发自适应
    config.boundary_threshold_factor = 0.2
    
    # 更高的细长形状阈值
    config.elongated_aspect_ratio = 3.0
    
    # 更保守的权重分配
    config.strategy_a_surface_weight = 0.6
    config.strategy_a_center_weight = 0.4
    
    config.strategy_b_surface_weight = 0.4
    config.strategy_b_center_weight = 0.6
    
    config.strategy_c_surface_weight = 0.3
    config.strategy_c_center_weight = 0.7
    
    return config


def get_debug_adaptive_config() -> AdaptiveDistanceConfig:
    """获取调试用的自适应配置"""
    config = get_default_adaptive_config()
    config.enable_debug_output = True
    config.log_strategy_selection = True
    return config


class AdaptiveDistanceAnalyzer:
    """自适应距离分析器"""
    
    def __init__(self, config: AdaptiveDistanceConfig = None):
        self.config = config or get_default_adaptive_config()
        self.strategy_usage_stats = {
            'A': 0,  # 接近边界且细长
            'B': 0,  # 接近边界但规整
            'C': 0,  # 远离边界但细长
            'D': 0   # 远离边界且规整
        }
    
    def analyze_tile_characteristics(self, bbox_size) -> Dict[str, any]:
        """分析Tile特征"""
        max_dimension = max(bbox_size[0], bbox_size[1], bbox_size[2])
        min_dimension = min(bbox_size[0], bbox_size[1], bbox_size[2])
        avg_dimension = (bbox_size[0] + bbox_size[1] + bbox_size[2]) / 3.0
        
        aspect_ratio = max_dimension / min_dimension if min_dimension > 0 else 1.0
        is_elongated = aspect_ratio > self.config.elongated_aspect_ratio
        
        return {
            'max_dimension': max_dimension,
            'min_dimension': min_dimension,
            'avg_dimension': avg_dimension,
            'aspect_ratio': aspect_ratio,
            'is_elongated': is_elongated,
            'boundary_threshold': max_dimension * self.config.boundary_threshold_factor
        }
    
    def determine_strategy(self, closest_distance: float, tile_characteristics: Dict) -> str:
        """确定使用的距离计算策略"""
        is_near_boundary = closest_distance < tile_characteristics['boundary_threshold']
        is_elongated = tile_characteristics['is_elongated']
        
        if is_near_boundary and is_elongated:
            strategy = 'A'
        elif is_near_boundary:
            strategy = 'B'
        elif is_elongated:
            strategy = 'C'
        else:
            strategy = 'D'
        
        self.strategy_usage_stats[strategy] += 1
        
        if self.config.log_strategy_selection:
            print(f"Strategy {strategy}: near_boundary={is_near_boundary}, "
                  f"elongated={is_elongated}, distance={closest_distance:.1f}")
        
        return strategy
    
    def get_strategy_weights(self, strategy: str) -> Tuple[float, float]:
        """获取策略对应的权重"""
        weight_map = {
            'A': (self.config.strategy_a_surface_weight, self.config.strategy_a_center_weight),
            'B': (self.config.strategy_b_surface_weight, self.config.strategy_b_center_weight),
            'C': (self.config.strategy_c_surface_weight, self.config.strategy_c_center_weight),
            'D': (self.config.strategy_d_surface_weight, self.config.strategy_d_center_weight)
        }
        return weight_map.get(strategy, (0.5, 0.5))
    
    def get_usage_statistics(self) -> Dict[str, any]:
        """获取策略使用统计"""
        total_calls = sum(self.strategy_usage_stats.values())
        if total_calls == 0:
            return {'total_calls': 0, 'strategy_percentages': {}}
        
        percentages = {
            strategy: (count / total_calls * 100)
            for strategy, count in self.strategy_usage_stats.items()
        }
        
        return {
            'total_calls': total_calls,
            'strategy_counts': self.strategy_usage_stats.copy(),
            'strategy_percentages': percentages
        }
    
    def print_statistics(self):
        """打印使用统计"""
        stats = self.get_usage_statistics()
        
        print(f"\n📊 自适应距离计算策略使用统计:")
        print(f"总调用次数: {stats['total_calls']}")
        
        if stats['total_calls'] > 0:
            print(f"策略使用分布:")
            for strategy in ['A', 'B', 'C', 'D']:
                count = stats['strategy_counts'][strategy]
                percentage = stats['strategy_percentages'][strategy]
                strategy_desc = {
                    'A': '接近边界且细长',
                    'B': '接近边界但规整',
                    'C': '远离边界但细长',
                    'D': '远离边界且规整'
                }[strategy]
                print(f"  策略{strategy} ({strategy_desc}): {count}次 ({percentage:.1f}%)")


def apply_adaptive_distance_config(scheduler, config: AdaptiveDistanceConfig = None):
    """
    将自适应距离配置应用到LOD调度器
    
    Args:
        scheduler: LOD调度器实例
        config: 自适应距离配置
    """
    if config is None:
        config = get_default_adaptive_config()
    
    # 创建分析器并附加到调度器
    analyzer = AdaptiveDistanceAnalyzer(config)
    
    # 将配置和分析器附加到调度器
    scheduler.adaptive_distance_config = config
    scheduler.adaptive_distance_analyzer = analyzer
    
    print(f"✅ 自适应距离配置已应用到LOD调度器")
    print(f"   边界阈值因子: {config.boundary_threshold_factor}")
    print(f"   细长形状阈值: {config.elongated_aspect_ratio}")
    print(f"   调试输出: {'启用' if config.enable_debug_output else '禁用'}")


def print_config_comparison():
    """打印不同配置的对比"""
    configs = {
        '默认配置': get_default_adaptive_config(),
        '激进配置': get_aggressive_adaptive_config(),
        '保守配置': get_conservative_adaptive_config()
    }
    
    print("\n📋 自适应距离配置对比:")
    print("="*80)
    print(f"{'参数':<25} {'默认配置':<15} {'激进配置':<15} {'保守配置':<15}")
    print("-"*80)
    
    for param_name in ['boundary_threshold_factor', 'elongated_aspect_ratio', 
                       'strategy_a_surface_weight', 'strategy_b_surface_weight']:
        values = []
        for config_name in ['默认配置', '激进配置', '保守配置']:
            config = configs[config_name]
            value = getattr(config, param_name)
            values.append(f"{value:.2f}")
        
        print(f"{param_name:<25} {values[0]:<15} {values[1]:<15} {values[2]:<15}")
    
    print("\n💡 配置选择建议:")
    print("  - 默认配置: 平衡性能和质量，适合大多数场景")
    print("  - 激进配置: 更强调视觉质量，适合高质量渲染需求")
    print("  - 保守配置: 更接近传统方法，适合性能敏感场景")


if __name__ == "__main__":
    print_config_comparison()
