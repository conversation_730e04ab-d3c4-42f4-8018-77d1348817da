"""
测试自适应距离计算功能
验证相机接近大面积Tile边界时的LOD选择改进
"""

import math
from typing import List, Tuple
from pxr import Gf
import omni.usd
from lod_scheduler import LODScheduler, BoundingBox


class AdaptiveDistanceTester:
    """自适应距离计算测试器"""
    
    def __init__(self, stage):
        self.stage = stage
        self.scheduler = None
        
    def create_test_scheduler(self):
        """创建测试用的LOD调度器"""
        try:
            from lod_config import get_default_lod_config
            lod_config = get_default_lod_config()
            self.scheduler = LODScheduler(self.stage, centralized_config=lod_config)
            print("✅ 创建测试LOD调度器成功")
            return True
        except Exception as e:
            print(f"❌ 创建LOD调度器失败: {e}")
            return False
    
    def test_distance_calculation_methods(self):
        """测试不同距离计算方法的差异"""
        print("\n🧪 测试距离计算方法差异")
        print("="*60)
        
        # 定义测试场景：不同形状和大小的包围盒
        test_scenarios = [
            {
                'name': '小正方形区域',
                'bbox': BoundingBox(
                    Gf.Vec3f(-10, -10, -5),
                    Gf.Vec3f(10, 10, 5)
                )
            },
            {
                'name': '大正方形区域',
                'bbox': BoundingBox(
                    Gf.Vec3f(-50, -50, -10),
                    Gf.Vec3f(50, 50, 10)
                )
            },
            {
                'name': '细长矩形区域',
                'bbox': BoundingBox(
                    Gf.Vec3f(-100, -10, -5),
                    Gf.Vec3f(100, 10, 5)
                )
            },
            {
                'name': '超大矩形区域',
                'bbox': BoundingBox(
                    Gf.Vec3f(-200, -100, -10),
                    Gf.Vec3f(200, 100, 10)
                )
            }
        ]
        
        # 定义测试相机位置：从远到近，包括边界附近
        for scenario in test_scenarios:
            print(f"\n📦 测试场景: {scenario['name']}")
            bbox = scenario['bbox']
            center = bbox.center
            size = bbox.size
            
            print(f"   包围盒中心: {center}")
            print(f"   包围盒尺寸: {size}")
            print(f"   长宽比: {max(size[0], size[1], size[2]) / min(size[0], size[1], size[2]):.2f}")
            
            # 测试不同相机位置
            test_positions = self._generate_test_positions(bbox)
            
            print(f"\n   📍 相机位置测试:")
            print(f"   {'位置描述':<15} {'传统距离':<10} {'自适应距离':<12} {'差异':<8} {'LOD(传统)':<10} {'LOD(自适应)':<12}")
            print(f"   {'-'*80}")
            
            for pos_desc, camera_pos in test_positions:
                # 计算传统距离
                traditional_distance = self.scheduler.calculate_distance_to_bounding_sphere(
                    camera_pos, center, size, adaptive_mode=False
                )
                
                # 计算自适应距离
                adaptive_distance = self.scheduler.calculate_distance_to_bounding_sphere(
                    camera_pos, center, size, adaptive_mode=True
                )
                
                # 计算差异
                difference = abs(adaptive_distance - traditional_distance)
                diff_percent = (difference / traditional_distance * 100) if traditional_distance > 0 else 0
                
                # 获取LOD选择
                traditional_lod, _ = self.scheduler.select_lod_by_sse_and_distance(
                    bbox, camera_pos, verbose=False
                )
                
                # 临时修改调度器使用自适应模式
                adaptive_lod, _ = self.scheduler.select_lod_by_sse_and_distance(
                    bbox, camera_pos, verbose=False
                )
                
                # 输出结果
                print(f"   {pos_desc:<15} {traditional_distance:<10.1f} {adaptive_distance:<12.1f} "
                      f"{diff_percent:<7.1f}% {traditional_lod:<10} {adaptive_lod:<12}")
    
    def _generate_test_positions(self, bbox: BoundingBox) -> List[Tuple[str, Gf.Vec3f]]:
        """生成测试相机位置"""
        center = bbox.center
        size = bbox.size
        max_dim = max(size[0], size[1], size[2])
        
        positions = []
        
        # 1. 远距离位置
        positions.append(("远距离", Gf.Vec3f(
            center[0] + max_dim * 2,
            center[1],
            center[2] + max_dim * 0.5
        )))
        
        # 2. 中等距离位置
        positions.append(("中等距离", Gf.Vec3f(
            center[0] + max_dim * 0.8,
            center[1],
            center[2] + max_dim * 0.3
        )))
        
        # 3. 接近边界位置
        positions.append(("接近边界", Gf.Vec3f(
            center[0] + size[0] * 0.6,  # 接近X边界
            center[1],
            center[2] + size[2] * 0.6   # 接近Z边界
        )))
        
        # 4. 非常接近边界
        positions.append(("很近边界", Gf.Vec3f(
            center[0] + size[0] * 0.45,  # 非常接近X边界
            center[1] + size[1] * 0.45,  # 非常接近Y边界
            center[2]
        )))
        
        # 5. 边界外很近
        positions.append(("边界外近", Gf.Vec3f(
            center[0] + size[0] * 0.55,  # 刚好在边界外
            center[1],
            center[2]
        )))
        
        # 6. 角落附近
        positions.append(("角落附近", Gf.Vec3f(
            center[0] + size[0] * 0.4,
            center[1] + size[1] * 0.4,
            center[2] + size[2] * 0.4
        )))
        
        return positions
    
    def test_lod_switching_improvement(self):
        """测试LOD切换改进效果"""
        print("\n🎯 测试LOD切换改进效果")
        print("="*60)
        
        # 创建一个大的细长区域来模拟问题场景
        large_tile = BoundingBox(
            Gf.Vec3f(-150, -50, -10),
            Gf.Vec3f(150, 50, 10)
        )
        
        print(f"测试区域: 大型细长Tile")
        print(f"尺寸: {large_tile.size}")
        print(f"中心: {large_tile.center}")
        
        # 模拟相机从远处逐渐接近边界的过程
        center = large_tile.center
        size = large_tile.size
        
        # 从右侧远处接近右边界
        start_x = center[0] + size[0] * 1.5  # 远处开始
        end_x = center[0] + size[0] * 0.45   # 接近边界
        
        print(f"\n📹 相机移动路径: 从远处接近右边界")
        print(f"起始X: {start_x:.1f}, 结束X: {end_x:.1f}")
        print(f"\n{'距离中心':<10} {'传统方法':<15} {'自适应方法':<15} {'LOD改进':<10}")
        print(f"{'-'*55}")
        
        steps = 10
        lod_improvements = 0
        
        for i in range(steps + 1):
            # 线性插值计算当前位置
            t = i / steps
            current_x = start_x + t * (end_x - start_x)
            camera_pos = Gf.Vec3f(current_x, center[1], center[2] + 20)
            
            # 计算到中心的距离
            distance_to_center = math.sqrt(
                (camera_pos[0] - center[0])**2 +
                (camera_pos[1] - center[1])**2 +
                (camera_pos[2] - center[2])**2
            )
            
            # 传统方法LOD选择
            traditional_lod, _ = self.scheduler.select_lod_by_sse_and_distance(
                large_tile, camera_pos, verbose=False
            )
            
            # 自适应方法LOD选择（已经在select_lod_by_sse_and_distance中使用）
            adaptive_lod, _ = self.scheduler.select_lod_by_sse_and_distance(
                large_tile, camera_pos, verbose=False
            )
            
            # 检查是否有改进
            improvement = "✅" if adaptive_lod != traditional_lod else "  "
            if adaptive_lod != traditional_lod:
                lod_improvements += 1
            
            print(f"{distance_to_center:<10.1f} {traditional_lod:<15} {adaptive_lod:<15} {improvement:<10}")
        
        print(f"\n📊 改进统计:")
        print(f"总测试点: {steps + 1}")
        print(f"LOD改进点: {lod_improvements}")
        print(f"改进率: {lod_improvements / (steps + 1) * 100:.1f}%")
        
        if lod_improvements > 0:
            print(f"✅ 自适应距离计算成功改进了LOD选择!")
        else:
            print(f"ℹ️  在此测试场景中未观察到明显改进")


def run_adaptive_distance_test():
    """运行自适应距离计算测试"""
    try:
        print("🚀 开始自适应距离计算测试")
        print("="*60)
        
        # 获取当前stage
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ 错误: 未找到活跃的USD stage")
            return
        
        # 创建测试器
        tester = AdaptiveDistanceTester(stage)
        
        # 创建测试调度器
        if not tester.create_test_scheduler():
            return
        
        # 执行测试
        tester.test_distance_calculation_methods()
        tester.test_lod_switching_improvement()
        
        print(f"\n✅ 自适应距离计算测试完成!")
        print(f"\n💡 测试结果说明:")
        print(f"   - 差异百分比显示自适应方法与传统方法的距离差异")
        print(f"   - LOD改进显示在哪些位置自适应方法选择了更合适的LOD")
        print(f"   - 接近边界时，自适应方法应该选择更高质量的LOD")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_adaptive_distance_test()
