"""
自适应距离计算可视化工具
帮助理解和调试自适应距离计算的工作原理
"""

import math
import matplotlib.pyplot as plt
import numpy as np
from typing import List, Tuple, Dict
from pxr import Gf


class AdaptiveDistanceVisualizer:
    """自适应距离计算可视化器"""
    
    def __init__(self):
        self.test_results = []
    
    def simulate_distance_calculation(self, bbox_size: Tuple[float, float, float],
                                    camera_positions: List[Tuple[float, float, float]],
                                    config_params: Dict = None) -> Dict:
        """
        模拟自适应距离计算
        
        Args:
            bbox_size: 包围盒尺寸 (width, height, depth)
            camera_positions: 相机位置列表
            config_params: 配置参数
            
        Returns:
            Dict: 计算结果
        """
        if config_params is None:
            config_params = {
                'boundary_threshold_factor': 0.3,
                'elongated_aspect_ratio': 2.0,
                'strategy_weights': {
                    'A': (0.8, 0.2),  # 接近边界且细长
                    'B': (0.6, 0.4),  # 接近边界但规整
                    'C': (0.4, 0.6),  # 远离边界但细长
                    'D': (0.2, 0.8)   # 远离边界且规整
                }
            }
        
        bbox_center = Gf.Vec3f(0, 0, 0)  # 假设包围盒在原点
        bbox_size_vec = Gf.Vec3f(bbox_size[0], bbox_size[1], bbox_size[2])
        
        results = {
            'positions': [],
            'traditional_distances': [],
            'adaptive_distances': [],
            'strategies': [],
            'improvements': [],
            'tile_characteristics': self._analyze_tile_characteristics(bbox_size_vec, config_params)
        }
        
        for pos in camera_positions:
            camera_pos = Gf.Vec3f(pos[0], pos[1], pos[2])
            
            # 计算传统距离（到中心）
            traditional_dist = self._calculate_center_distance(camera_pos, bbox_center)
            
            # 计算自适应距离
            adaptive_dist, strategy = self._calculate_adaptive_distance_with_strategy(
                camera_pos, bbox_center, bbox_size_vec, config_params
            )
            
            # 计算改进程度
            improvement = abs(adaptive_dist - traditional_dist) / traditional_dist * 100
            
            results['positions'].append(pos)
            results['traditional_distances'].append(traditional_dist)
            results['adaptive_distances'].append(adaptive_dist)
            results['strategies'].append(strategy)
            results['improvements'].append(improvement)
        
        return results
    
    def _analyze_tile_characteristics(self, bbox_size: Gf.Vec3f, config_params: Dict) -> Dict:
        """分析Tile特征"""
        max_dimension = max(bbox_size[0], bbox_size[1], bbox_size[2])
        min_dimension = min(bbox_size[0], bbox_size[1], bbox_size[2])
        avg_dimension = (bbox_size[0] + bbox_size[1] + bbox_size[2]) / 3.0
        
        aspect_ratio = max_dimension / min_dimension if min_dimension > 0 else 1.0
        is_elongated = aspect_ratio > config_params['elongated_aspect_ratio']
        boundary_threshold = max_dimension * config_params['boundary_threshold_factor']
        
        return {
            'max_dimension': max_dimension,
            'min_dimension': min_dimension,
            'avg_dimension': avg_dimension,
            'aspect_ratio': aspect_ratio,
            'is_elongated': is_elongated,
            'boundary_threshold': boundary_threshold
        }
    
    def _calculate_center_distance(self, camera_pos: Gf.Vec3f, bbox_center: Gf.Vec3f) -> float:
        """计算到中心的距离"""
        return math.sqrt(
            (camera_pos[0] - bbox_center[0])**2 +
            (camera_pos[1] - bbox_center[1])**2 +
            (camera_pos[2] - bbox_center[2])**2
        )
    
    def _calculate_surface_distance(self, camera_pos: Gf.Vec3f, bbox_center: Gf.Vec3f, 
                                  bbox_size: Gf.Vec3f) -> float:
        """计算到表面的距离"""
        min_point = Gf.Vec3f(
            bbox_center[0] - bbox_size[0] / 2.0,
            bbox_center[1] - bbox_size[1] / 2.0,
            bbox_center[2] - bbox_size[2] / 2.0
        )
        max_point = Gf.Vec3f(
            bbox_center[0] + bbox_size[0] / 2.0,
            bbox_center[1] + bbox_size[1] / 2.0,
            bbox_center[2] + bbox_size[2] / 2.0
        )
        
        # 计算点到包围盒各个轴向的距离
        dx = max(0, max(min_point[0] - camera_pos[0], camera_pos[0] - max_point[0]))
        dy = max(0, max(min_point[1] - camera_pos[1], camera_pos[1] - max_point[1]))
        dz = max(0, max(min_point[2] - camera_pos[2], camera_pos[2] - max_point[2]))
        
        return math.sqrt(dx*dx + dy*dy + dz*dz)
    
    def _calculate_adaptive_distance_with_strategy(self, camera_pos: Gf.Vec3f, bbox_center: Gf.Vec3f,
                                                 bbox_size: Gf.Vec3f, config_params: Dict) -> Tuple[float, str]:
        """计算自适应距离并返回使用的策略"""
        # 计算基础距离
        center_distance = self._calculate_center_distance(camera_pos, bbox_center)
        surface_distance = self._calculate_surface_distance(camera_pos, bbox_center, bbox_size)
        
        # 获取Tile特征
        tile_chars = self._analyze_tile_characteristics(bbox_size, config_params)
        
        # 判断相机位置特征
        is_near_boundary = surface_distance < tile_chars['boundary_threshold']
        is_elongated = tile_chars['is_elongated']
        
        # 确定策略
        if is_near_boundary and is_elongated:
            strategy = 'A'
        elif is_near_boundary:
            strategy = 'B'
        elif is_elongated:
            strategy = 'C'
        else:
            strategy = 'D'
        
        # 获取权重
        weight_surface, weight_center = config_params['strategy_weights'][strategy]
        
        # 计算自适应距离
        adaptive_distance = (surface_distance * weight_surface + center_distance * weight_center)
        
        # 应用最小距离约束
        min_distance = tile_chars['avg_dimension'] * 0.1
        final_distance = max(adaptive_distance, min_distance)
        
        return final_distance, strategy
    
    def create_visualization(self, bbox_size: Tuple[float, float, float], 
                           grid_resolution: int = 20) -> None:
        """创建可视化图表"""
        print("🎨 创建自适应距离计算可视化...")
        
        # 生成网格相机位置
        max_dim = max(bbox_size)
        positions = []
        
        # 在XZ平面上创建网格
        x_range = np.linspace(-max_dim, max_dim, grid_resolution)
        z_range = np.linspace(-max_dim, max_dim, grid_resolution)
        
        for x in x_range:
            for z in z_range:
                positions.append((x, 0, z))  # Y=0平面
        
        # 计算距离
        results = self.simulate_distance_calculation(bbox_size, positions)
        
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 重塑数据为网格
        X, Z = np.meshgrid(x_range, z_range)
        traditional_grid = np.array(results['traditional_distances']).reshape(grid_resolution, grid_resolution)
        adaptive_grid = np.array(results['adaptive_distances']).reshape(grid_resolution, grid_resolution)
        improvement_grid = np.array(results['improvements']).reshape(grid_resolution, grid_resolution)
        
        # 策略分布
        strategy_map = {'A': 4, 'B': 3, 'C': 2, 'D': 1}
        strategy_grid = np.array([strategy_map[s] for s in results['strategies']]).reshape(grid_resolution, grid_resolution)
        
        # 绘制传统距离
        im1 = ax1.contourf(X, Z, traditional_grid, levels=20, cmap='viridis')
        ax1.set_title('传统距离计算 (到中心)')
        ax1.set_xlabel('X 位置')
        ax1.set_ylabel('Z 位置')
        plt.colorbar(im1, ax=ax1, label='距离')
        
        # 绘制自适应距离
        im2 = ax2.contourf(X, Z, adaptive_grid, levels=20, cmap='viridis')
        ax2.set_title('自适应距离计算')
        ax2.set_xlabel('X 位置')
        ax2.set_ylabel('Z 位置')
        plt.colorbar(im2, ax=ax2, label='距离')
        
        # 绘制改进程度
        im3 = ax3.contourf(X, Z, improvement_grid, levels=20, cmap='RdYlBu_r')
        ax3.set_title('改进程度 (%)')
        ax3.set_xlabel('X 位置')
        ax3.set_ylabel('Z 位置')
        plt.colorbar(im3, ax=ax3, label='改进百分比')
        
        # 绘制策略分布
        im4 = ax4.contourf(X, Z, strategy_grid, levels=[0.5, 1.5, 2.5, 3.5, 4.5], 
                          colors=['red', 'orange', 'yellow', 'green'], alpha=0.7)
        ax4.set_title('策略分布')
        ax4.set_xlabel('X 位置')
        ax4.set_ylabel('Z 位置')
        
        # 添加策略图例
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='red', label='策略D: 远离边界且规整'),
            Patch(facecolor='orange', label='策略C: 远离边界但细长'),
            Patch(facecolor='yellow', label='策略B: 接近边界但规整'),
            Patch(facecolor='green', label='策略A: 接近边界且细长')
        ]
        ax4.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.3, 1))
        
        # 添加包围盒轮廓
        for ax in [ax1, ax2, ax3, ax4]:
            rect_x = [-bbox_size[0]/2, bbox_size[0]/2, bbox_size[0]/2, -bbox_size[0]/2, -bbox_size[0]/2]
            rect_z = [-bbox_size[2]/2, -bbox_size[2]/2, bbox_size[2]/2, bbox_size[2]/2, -bbox_size[2]/2]
            ax.plot(rect_x, rect_z, 'k--', linewidth=2, label='Tile边界')
        
        plt.tight_layout()
        plt.suptitle(f'自适应距离计算可视化\nTile尺寸: {bbox_size}', y=1.02)
        
        # 显示统计信息
        tile_chars = results['tile_characteristics']
        print(f"\n📊 Tile特征分析:")
        print(f"   长宽比: {tile_chars['aspect_ratio']:.2f}")
        print(f"   是否细长: {'是' if tile_chars['is_elongated'] else '否'}")
        print(f"   边界阈值: {tile_chars['boundary_threshold']:.1f}")
        
        strategy_counts = {}
        for strategy in results['strategies']:
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
        
        print(f"\n📈 策略使用统计:")
        for strategy, count in strategy_counts.items():
            percentage = count / len(results['strategies']) * 100
            print(f"   策略{strategy}: {count}次 ({percentage:.1f}%)")
        
        plt.show()


def create_comparison_visualization():
    """创建不同Tile形状的对比可视化"""
    visualizer = AdaptiveDistanceVisualizer()
    
    # 定义不同的测试场景
    scenarios = [
        {
            'name': '小正方形',
            'size': (20, 20, 10),
            'description': '规整形状，小尺寸'
        },
        {
            'name': '大正方形', 
            'size': (100, 100, 20),
            'description': '规整形状，大尺寸'
        },
        {
            'name': '细长矩形',
            'size': (200, 40, 10),
            'description': '细长形状，大尺寸'
        },
        {
            'name': '超长条形',
            'size': (300, 20, 5),
            'description': '极细长形状'
        }
    ]
    
    print("🎨 创建多场景对比可视化...")
    
    for i, scenario in enumerate(scenarios):
        print(f"\n📦 场景 {i+1}: {scenario['name']} - {scenario['description']}")
        print(f"   尺寸: {scenario['size']}")
        
        # 为每个场景创建可视化
        visualizer.create_visualization(scenario['size'], grid_resolution=15)


if __name__ == "__main__":
    print("🎯 自适应距离计算可视化工具")
    print("="*50)
    
    try:
        # 检查是否有matplotlib
        import matplotlib.pyplot as plt
        
        print("选择可视化类型:")
        print("1. 单个场景详细分析")
        print("2. 多场景对比分析")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            # 单个场景分析
            visualizer = AdaptiveDistanceVisualizer()
            # 使用一个大的细长Tile作为示例
            visualizer.create_visualization((150, 50, 10), grid_resolution=20)
            
        elif choice == "2":
            # 多场景对比
            create_comparison_visualization()
            
        elif choice == "3":
            print("👋 退出")
            
        else:
            print("无效选择")
            
    except ImportError:
        print("❌ 需要安装matplotlib库才能使用可视化功能")
        print("请运行: pip install matplotlib")
    except Exception as e:
        print(f"❌ 可视化过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
