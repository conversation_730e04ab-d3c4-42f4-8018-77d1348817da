#!/usr/bin/env python3
"""
测试固定距离LOD一致性的脚本

这个脚本用于验证固定距离方法是否正确避免了多个LOD级别同时显示的问题。
"""

import omni.usd
from pxr import Usd, UsdGeom, Gf
from lod_scheduler import LODScheduler, TilesetLODManager
from lod_config import get_default_lod_config

def test_lod_consistency():
    """测试LOD一致性，确保固定距离方法不会混合显示多个LOD级别"""
    
    # 获取当前stage
    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("ERROR: No USD stage found")
        return False
    
    # 创建LOD调度器和管理器
    config = get_default_lod_config()
    scheduler = LODScheduler(stage, camera_path="/World/Camera", centralized_config=config)
    tileset_manager = TilesetLODManager(stage, camera_path="/World/Camera", 
                                       lod_scheduler=scheduler, config=config)
    
    print("=== 测试固定距离LOD一致性 ===")
    
    # 测试1：使用固定距离方法
    print("\n1. 测试固定距离方法（应该只显示单一LOD级别）:")
    result_fixed = tileset_manager.update_tileset_lod_visibility(
        use_fixed_distance=True, verbose=True
    )
    
    if result_fixed[0]:
        selected_lod, center_distance, avg_distance = result_fixed
        print(f"\n✅ 固定距离方法结果:")
        print(f"   选择的LOD: {selected_lod}")
        print(f"   中心距离: {center_distance:.1f}m")
        print(f"   平均距离: {avg_distance:.1f}m")
        
        # 检查是否只有一个LOD级别被选择
        lod_distribution = check_visible_lod_distribution(stage)
        active_lods = [lod for lod, count in lod_distribution.items() if count > 0]
        
        if len(active_lods) == 1:
            print(f"   ✅ 一致性检查通过: 只显示 {active_lods[0]} LOD")
            consistency_passed = True
        else:
            print(f"   ❌ 一致性检查失败: 显示了多个LOD级别 {active_lods}")
            consistency_passed = False
    else:
        print("❌ 固定距离方法更新失败")
        consistency_passed = False
    
    # 测试2：对比SSE方法
    print("\n" + "="*60)
    print("2. 对比SSE方法（可能显示混合LOD级别）:")
    result_sse = tileset_manager.update_tileset_lod_visibility(
        use_fixed_distance=False, verbose=True
    )
    
    if result_sse[0]:
        selected_lod_sse, center_distance_sse, avg_sse = result_sse
        print(f"\n✅ SSE方法结果:")
        print(f"   选择的LOD: {selected_lod_sse}")
        print(f"   中心距离: {center_distance_sse:.1f}m")
        print(f"   平均SSE: {avg_sse:.2f}px")
        
        # 检查SSE方法的LOD分布
        lod_distribution_sse = check_visible_lod_distribution(stage)
        active_lods_sse = [lod for lod, count in lod_distribution_sse.items() if count > 0]
        
        print(f"   SSE方法显示的LOD级别: {active_lods_sse}")
        if len(active_lods_sse) > 1:
            print(f"   ℹ️  SSE方法显示了混合LOD（这是正常的）")
        else:
            print(f"   ℹ️  SSE方法也只显示了单一LOD")
    else:
        print("❌ SSE方法更新失败")
    
    # 测试3：多次切换测试稳定性
    print("\n" + "="*60)
    print("3. 测试多次切换的稳定性:")
    
    for i in range(3):
        print(f"\n   第 {i+1} 次切换到固定距离方法:")
        result = tileset_manager.update_tileset_lod_visibility(
            use_fixed_distance=True, verbose=False
        )
        
        if result[0]:
            lod_dist = check_visible_lod_distribution(stage)
            active = [lod for lod, count in lod_dist.items() if count > 0]
            print(f"     显示的LOD: {active}")
            
            if len(active) == 1:
                print(f"     ✅ 第 {i+1} 次一致性检查通过")
            else:
                print(f"     ❌ 第 {i+1} 次一致性检查失败")
                consistency_passed = False
        else:
            print(f"     ❌ 第 {i+1} 次更新失败")
            consistency_passed = False
    
    # 总结
    print("\n" + "="*60)
    print("📊 测试总结:")
    if consistency_passed:
        print("✅ 固定距离方法一致性测试通过！")
        print("   - 成功避免了多个LOD级别同时显示")
        print("   - 每次更新都只显示单一LOD级别")
    else:
        print("❌ 固定距离方法一致性测试失败！")
        print("   - 仍然存在多个LOD级别同时显示的问题")
    
    return consistency_passed

def check_visible_lod_distribution(stage):
    """检查当前可见的LOD分布"""
    lod_distribution = {"High": 0, "Medium": 0, "Low": 0, "VeryLow": 0}
    
    try:
        # 查找TilesetRegion
        region_path = "/World/TilesetRegion"
        region_prim = stage.GetPrimAtPath(region_path)
        
        if not region_prim or not region_prim.IsValid():
            print(f"Warning: TilesetRegion not found at {region_path}")
            return lod_distribution
        
        def count_visible_content(prim):
            """递归统计可见的内容节点"""
            prim_name = prim.GetName()
            
            # 检查是否是内容节点
            if prim_name.startswith("Content_LOD_"):
                # 检查可见性
                imageable = UsdGeom.Imageable(prim)
                if imageable:
                    vis_attr = imageable.GetVisibilityAttr()
                    if vis_attr:
                        visibility = vis_attr.Get()
                        if visibility == UsdGeom.Tokens.inherited:
                            # 获取LOD级别
                            lod_level_attr = prim.GetAttribute("tileset:lodLevel")
                            if lod_level_attr:
                                lod_level = lod_level_attr.Get()
                                # 将数值LOD级别转换为名称
                                if lod_level == 20:
                                    lod_distribution["High"] += 1
                                elif lod_level == 18:
                                    lod_distribution["Medium"] += 1
                                elif lod_level == 16:
                                    lod_distribution["Low"] += 1
                                elif lod_level == 14:
                                    lod_distribution["VeryLow"] += 1
            
            # 递归处理子节点
            for child in prim.GetChildren():
                count_visible_content(child)
        
        # 从TilesetRegion开始统计
        count_visible_content(region_prim)
        
    except Exception as e:
        print(f"Error checking LOD distribution: {e}")
    
    return lod_distribution

def print_current_camera_info():
    """打印当前相机信息"""
    stage = omni.usd.get_context().get_stage()
    if not stage:
        return
    
    try:
        camera = stage.GetPrimAtPath("/World/Camera")
        if camera:
            xformable = UsdGeom.Xformable(camera)
            transform = xformable.GetLocalTransformation()
            position = transform.ExtractTranslation()
            print(f"当前相机位置: ({position[0]:.1f}, {position[1]:.1f}, {position[2]:.1f})")
        else:
            print("未找到相机")
    except Exception as e:
        print(f"获取相机信息失败: {e}")

if __name__ == "__main__":
    print("固定距离LOD一致性测试脚本")
    print("请在Isaac Sim中运行此脚本")
    
    try:
        # 打印相机信息
        print_current_camera_info()
        
        # 运行一致性测试
        success = test_lod_consistency()
        
        if success:
            print("\n🎉 所有测试通过！固定距离方法工作正常。")
        else:
            print("\n⚠️  测试发现问题，请检查实现。")
            
    except Exception as e:
        print(f"测试运行出错: {e}")
        import traceback
        traceback.print_exc()
