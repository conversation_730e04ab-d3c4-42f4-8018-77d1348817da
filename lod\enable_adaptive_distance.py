"""
启用自适应距离计算功能
解决大面积Tile在相机接近边界时仍显示低LOD的问题
"""

import omni.usd
from adaptive_distance_config import (
    get_default_adaptive_config,
    get_aggressive_adaptive_config,
    get_conservative_adaptive_config,
    apply_adaptive_distance_config,
    AdaptiveDistanceAnalyzer
)


def enable_adaptive_distance_for_existing_system(config_type: str = "default"):
    """
    为现有系统启用自适应距离计算
    
    Args:
        config_type: 配置类型 ("default", "aggressive", "conservative", "debug")
    """
    try:
        print("🚀 启用自适应距离计算功能...")
        print("="*60)
        
        # 1. 获取当前stage
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ 错误: 未找到活跃的USD stage")
            return False
        
        print("✅ 找到活跃的USD stage")
        
        # 2. 选择配置
        config_map = {
            "default": get_default_adaptive_config(),
            "aggressive": get_aggressive_adaptive_config(),
            "conservative": get_conservative_adaptive_config(),
            "debug": get_default_adaptive_config()
        }
        
        if config_type == "debug":
            config = config_map["debug"]
            config.enable_debug_output = True
            config.log_strategy_selection = True
        else:
            config = config_map.get(config_type, get_default_adaptive_config())
        
        print(f"📋 使用配置类型: {config_type}")
        print(f"   边界阈值因子: {config.boundary_threshold_factor}")
        print(f"   细长形状阈值: {config.elongated_aspect_ratio}")
        print(f"   调试输出: {'启用' if config.enable_debug_output else '禁用'}")
        
        # 3. 查找现有的LOD调度器
        scheduler = find_existing_lod_scheduler()
        
        if scheduler:
            print("✅ 找到现有的LOD调度器")
            
            # 应用自适应距离配置
            apply_adaptive_distance_config(scheduler, config)
            
            print("✅ 自适应距离计算功能已启用!")
            
            # 显示使用说明
            print_usage_instructions()
            
            return True
            
        else:
            print("⚠️  未找到现有的LOD调度器")
            print("💡 尝试创建新的调度器...")
            
            scheduler = create_new_lod_scheduler_with_adaptive_distance(config)
            if scheduler:
                print("✅ 创建了新的LOD调度器并启用自适应距离计算")
                return True
            else:
                print("❌ 无法创建LOD调度器")
                return False
        
    except Exception as e:
        print(f"❌ 启用自适应距离计算时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def find_existing_lod_scheduler():
    """查找现有的LOD调度器"""
    try:
        # 尝试从全局变量或模块中查找
        import sys
        
        # 检查主模块中是否有调度器
        main_module = sys.modules.get('__main__', None)
        if main_module:
            # 检查常见的调度器变量名
            for attr_name in ['scheduler', 'lod_scheduler', 'tileset_manager']:
                if hasattr(main_module, attr_name):
                    obj = getattr(main_module, attr_name)
                    # 检查是否是LODScheduler或包含LODScheduler的对象
                    if hasattr(obj, 'calculate_distance_to_bounding_sphere'):
                        return obj
                    elif hasattr(obj, 'lod_scheduler'):
                        return obj.lod_scheduler
        
        return None
        
    except Exception as e:
        print(f"查找LOD调度器时出错: {e}")
        return None


def create_new_lod_scheduler_with_adaptive_distance(config):
    """创建新的LOD调度器并启用自适应距离计算"""
    try:
        from lod_scheduler import LODScheduler
        from lod_config import get_default_lod_config
        
        stage = omni.usd.get_context().get_stage()
        if not stage:
            return None
        
        # 创建LOD调度器
        lod_config = get_default_lod_config()
        scheduler = LODScheduler(stage, centralized_config=lod_config)
        
        # 应用自适应距离配置
        apply_adaptive_distance_config(scheduler, config)
        
        return scheduler
        
    except Exception as e:
        print(f"创建LOD调度器时出错: {e}")
        return None


def print_usage_instructions():
    """打印使用说明"""
    print(f"\n📖 自适应距离计算使用说明:")
    print(f"="*60)
    print(f"✨ 功能特点:")
    print(f"   • 智能检测相机是否接近Tile边界")
    print(f"   • 根据Tile形状（细长vs规整）调整计算策略")
    print(f"   • 动态混合表面距离和中心距离")
    print(f"   • 减少大面积Tile边界附近的LOD延迟")
    print(f"")
    print(f"🎯 适用场景:")
    print(f"   • 大面积的地形或建筑Tile")
    print(f"   • 细长形状的道路、河流等Tile")
    print(f"   • 需要精确LOD控制的场景")
    print(f"")
    print(f"🔧 测试方法:")
    print(f"   1. 将相机移动到大Tile的边界附近")
    print(f"   2. 观察LOD是否比之前更早切换到高质量")
    print(f"   3. 运行测试脚本验证改进效果")
    print(f"")
    print(f"📊 监控统计:")
    print(f"   • 使用 scheduler.adaptive_distance_analyzer.print_statistics()")
    print(f"   • 查看不同策略的使用频率")


def test_adaptive_distance_improvement():
    """测试自适应距离改进效果"""
    try:
        print("\n🧪 测试自适应距离改进效果...")
        
        from test_adaptive_distance import run_adaptive_distance_test
        run_adaptive_distance_test()
        
    except Exception as e:
        print(f"测试时出错: {e}")


def show_adaptive_statistics():
    """显示自适应距离使用统计"""
    try:
        scheduler = find_existing_lod_scheduler()
        if scheduler and hasattr(scheduler, 'adaptive_distance_analyzer'):
            analyzer = scheduler.adaptive_distance_analyzer
            analyzer.print_statistics()
        else:
            print("未找到启用自适应距离的调度器")
            
    except Exception as e:
        print(f"显示统计信息时出错: {e}")


def main():
    """主函数"""
    print("🎯 自适应距离计算启用工具")
    print("解决大面积Tile边界附近LOD切换延迟问题")
    print("="*60)
    
    print("\n选择配置类型:")
    print("1. 默认配置 (平衡性能和质量)")
    print("2. 激进配置 (更强调视觉质量)")
    print("3. 保守配置 (更接近传统方法)")
    print("4. 调试配置 (启用详细输出)")
    print("5. 测试改进效果")
    print("6. 显示使用统计")
    print("7. 退出")
    
    try:
        choice = input("\n请输入选择 (1-7): ").strip()
        
        config_map = {
            "1": "default",
            "2": "aggressive", 
            "3": "conservative",
            "4": "debug"
        }
        
        if choice in config_map:
            config_type = config_map[choice]
            success = enable_adaptive_distance_for_existing_system(config_type)
            
            if success:
                print(f"\n🎉 自适应距离计算已成功启用!")
                print(f"现在可以测试相机在大Tile边界附近的LOD切换效果")
            else:
                print(f"\n❌ 启用失败，请检查系统状态")
                
        elif choice == "5":
            test_adaptive_distance_improvement()
            
        elif choice == "6":
            show_adaptive_statistics()
            
        elif choice == "7":
            print("👋 退出")
            
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出")
    except Exception as e:
        print(f"处理用户输入时出错: {e}")


if __name__ == "__main__":
    main()
